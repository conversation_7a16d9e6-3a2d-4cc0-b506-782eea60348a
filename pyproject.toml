[project]
name = "nomad_ragbot"
version = "0.1.0"
description = "A prototype chatbot built with retrieval-augmented generation (RAG) to assist researchers and developers working with the NOMAD platform."
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "google-genai>=1.28.0",
    "gradio>=5.41.0",
    "ipykernel>=6.30.1",
    "python-dotenv>=1.1.1",
    "beautifulsoup4",
    "lxml",
    "ipywidgets>=8.1.7",
]

[project.optional-dependencies]
eval = [
    "pandas>=2.2",
    "numpy>=1.26",
    "tqdm>=4.66",
    "streamlit>=1.36",
    "sentence-transformers>=3.0; python_version>='3.9'",
    "openai>=1.40",
    "pyarrow>=15.0"
]

[project.scripts]
ragbot-eval = "nomad_ragbot.eval.pipeline:main"
ragbot-eval-dash = "nomad_ragbot.dash.streamlit_app:main"